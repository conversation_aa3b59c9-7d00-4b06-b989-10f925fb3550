﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Dalamud.NET.Sdk/13.0.0">
  <PropertyGroup>
    <Authors></Authors>
    <Company></Company>
    <Version>1.11.0</Version>
    <Description></Description>
    <Copyright></Copyright>
    <PackageProjectUrl>https://github.com/Penumbra-Sync/client</PackageProjectUrl>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0-windows7.0</TargetFramework>
    <Platforms>x64</Platforms>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <ProduceReferenceAssembly>false</ProduceReferenceAssembly>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <CopyLocalLockfileAssemblies>true</CopyLocalLockfileAssemblies>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="PlayerData\Export\**" />
    <EmbeddedResource Remove="PlayerData\Export\**" />
    <None Remove="PlayerData\Export\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DalamudPackager" Version="12.0.0" />
    <PackageReference Include="Downloader" Version="3.3.4" />
    <PackageReference Include="K4os.Compression.LZ4.Legacy" Version="1.3.8" />
    <PackageReference Include="Meziantou.Analyzer" Version="2.0.189">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.MessagePack" Version="9.0.3" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.3" />
    <PackageReference Include="Glamourer.Api" Version="2.4.0" />
    <PackageReference Include="NReco.Logging.File" Version="1.2.2" />
    <PackageReference Include="Penumbra.Api" Version="5.6.0" />
    <PackageReference Include="Penumbra.String" Version="1.0.5" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
    <PackageReference Include="SonarAnalyzer.CSharp" Version="10.7.0.110445">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.7.0" />
  </ItemGroup>

  <PropertyGroup>
    <SourceRevisionId>build$([System.DateTime]::UtcNow.ToString("yyyy-MM-ddTHH:mm:ss:fffZ"))</SourceRevisionId>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\MareAPI\MareSynchronosAPI\MareSynchronos.API.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="CheapLoc">
      <HintPath>$(DalamudLibPath)CheapLoc.dll</HintPath>
      <Private>false</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Update="images\icon.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <EmbeddedResource Include="Localization\de.json" />
    <EmbeddedResource Include="Localization\fr.json" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\.editorconfig" Link=".editorconfig" />
  </ItemGroup>

</Project>
