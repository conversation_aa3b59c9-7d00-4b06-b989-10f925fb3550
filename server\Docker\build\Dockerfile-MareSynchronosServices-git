FROM mcr.microsoft.com/dotnet/sdk:8.0 as BUILD

RUN git clone --recurse-submodules https://github.com/Penumbra-Sync/server

WORKDIR /server/MareSynchronosServer/MareSynchronosServices/

RUN dotnet publish \
        --configuration=Release \
        --os=linux \
        --output=/MareSynchronosServices \
        MareSynchronosServices.csproj

FROM mcr.microsoft.com/dotnet/aspnet:8.0

RUN adduser \
        --disabled-password \
        --group \
        --no-create-home \
        --quiet \
        --system \
        mare

COPY --from=BUILD /MareSynchronosServices /opt/MareSynchronosServices
RUN chown -R mare:mare /opt/MareSynchronosServices
RUN apt-get update; apt-get install curl -y

USER mare:mare
WORKDIR /opt/MareSynchronosServices

CMD ["./MareSynchronosServices"]
