﻿// <auto-generated />
using System;
using MareSynchronosShared.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace MareSynchronosServer.Migrations
{
    [DbContext(typeof(MareDbContext))]
    [Migration("20241227190944_OrigFileGamePath")]
    partial class OrigFileGamePath
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("MareSynchronosShared.Models.Auth", b =>
                {
                    b.Property<string>("HashedKey")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("hashed_key");

                    b.Property<bool>("IsBanned")
                        .HasColumnType("boolean")
                        .HasColumnName("is_banned");

                    b.Property<bool>("MarkForBan")
                        .HasColumnType("boolean")
                        .HasColumnName("mark_for_ban");

                    b.Property<string>("PrimaryUserUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("primary_user_uid");

                    b.Property<string>("UserUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("user_uid");

                    b.HasKey("HashedKey")
                        .HasName("pk_auth");

                    b.HasIndex("PrimaryUserUID")
                        .HasDatabaseName("ix_auth_primary_user_uid");

                    b.HasIndex("UserUID")
                        .HasDatabaseName("ix_auth_user_uid");

                    b.ToTable("auth", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.Banned", b =>
                {
                    b.Property<string>("CharacterIdentification")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("character_identification");

                    b.Property<string>("Reason")
                        .HasColumnType("text")
                        .HasColumnName("reason");

                    b.Property<byte[]>("Timestamp")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("timestamp");

                    b.HasKey("CharacterIdentification")
                        .HasName("pk_banned_users");

                    b.ToTable("banned_users", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.BannedRegistrations", b =>
                {
                    b.Property<string>("DiscordIdOrLodestoneAuth")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("discord_id_or_lodestone_auth");

                    b.HasKey("DiscordIdOrLodestoneAuth")
                        .HasName("pk_banned_registrations");

                    b.ToTable("banned_registrations", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaData", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<string>("UploaderUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("uploader_uid");

                    b.Property<int>("AccessType")
                        .HasColumnType("integer")
                        .HasColumnName("access_type");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("CustomizeData")
                        .HasColumnType("text")
                        .HasColumnName("customize_data");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<int>("DownloadCount")
                        .HasColumnType("integer")
                        .HasColumnName("download_count");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expiry_date");

                    b.Property<string>("GlamourerData")
                        .HasColumnType("text")
                        .HasColumnName("glamourer_data");

                    b.Property<int>("ShareType")
                        .HasColumnType("integer")
                        .HasColumnName("share_type");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_date");

                    b.HasKey("Id", "UploaderUID")
                        .HasName("pk_chara_data");

                    b.HasIndex("Id")
                        .HasDatabaseName("ix_chara_data_id");

                    b.HasIndex("UploaderUID")
                        .HasDatabaseName("ix_chara_data_uploader_uid");

                    b.ToTable("chara_data", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaDataAllowance", b =>
                {
                    b.Property<string>("ParentId")
                        .HasColumnType("text")
                        .HasColumnName("parent_id");

                    b.Property<string>("ParentUploaderUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("parent_uploader_uid");

                    b.Property<string>("AllowedUserUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("allowed_user_uid");

                    b.HasKey("ParentId", "ParentUploaderUID", "AllowedUserUID")
                        .HasName("pk_chara_data_allowance");

                    b.HasIndex("AllowedUserUID")
                        .HasDatabaseName("ix_chara_data_allowance_allowed_user_uid");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_chara_data_allowance_parent_id");

                    b.ToTable("chara_data_allowance", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaDataFile", b =>
                {
                    b.Property<string>("ParentId")
                        .HasColumnType("text")
                        .HasColumnName("parent_id");

                    b.Property<string>("ParentUploaderUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("parent_uploader_uid");

                    b.Property<string>("GamePath")
                        .HasColumnType("text")
                        .HasColumnName("game_path");

                    b.Property<string>("FileCacheHash")
                        .HasColumnType("character varying(40)")
                        .HasColumnName("file_cache_hash");

                    b.HasKey("ParentId", "ParentUploaderUID", "GamePath")
                        .HasName("pk_chara_data_files");

                    b.HasIndex("FileCacheHash")
                        .HasDatabaseName("ix_chara_data_files_file_cache_hash");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_chara_data_files_parent_id");

                    b.ToTable("chara_data_files", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaDataFileSwap", b =>
                {
                    b.Property<string>("ParentId")
                        .HasColumnType("text")
                        .HasColumnName("parent_id");

                    b.Property<string>("ParentUploaderUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("parent_uploader_uid");

                    b.Property<string>("GamePath")
                        .HasColumnType("text")
                        .HasColumnName("game_path");

                    b.Property<string>("FilePath")
                        .HasColumnType("text")
                        .HasColumnName("file_path");

                    b.HasKey("ParentId", "ParentUploaderUID", "GamePath")
                        .HasName("pk_chara_data_file_swaps");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_chara_data_file_swaps_parent_id");

                    b.ToTable("chara_data_file_swaps", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaDataOriginalFile", b =>
                {
                    b.Property<string>("ParentId")
                        .HasColumnType("text")
                        .HasColumnName("parent_id");

                    b.Property<string>("ParentUploaderUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("parent_uploader_uid");

                    b.Property<string>("GamePath")
                        .HasColumnType("text")
                        .HasColumnName("game_path");

                    b.Property<string>("Hash")
                        .HasColumnType("text")
                        .HasColumnName("hash");

                    b.HasKey("ParentId", "ParentUploaderUID", "GamePath")
                        .HasName("pk_chara_data_orig_files");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_chara_data_orig_files_parent_id");

                    b.ToTable("chara_data_orig_files", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaDataPose", b =>
                {
                    b.Property<string>("ParentId")
                        .HasColumnType("text")
                        .HasColumnName("parent_id");

                    b.Property<string>("ParentUploaderUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("parent_uploader_uid");

                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("PoseData")
                        .HasColumnType("text")
                        .HasColumnName("pose_data");

                    b.Property<string>("WorldData")
                        .HasColumnType("text")
                        .HasColumnName("world_data");

                    b.HasKey("ParentId", "ParentUploaderUID", "Id")
                        .HasName("pk_chara_data_poses");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("ix_chara_data_poses_parent_id");

                    b.ToTable("chara_data_poses", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.ClientPair", b =>
                {
                    b.Property<string>("UserUID")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("user_uid");

                    b.Property<string>("OtherUserUID")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("other_user_uid");

                    b.Property<byte[]>("Timestamp")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("timestamp");

                    b.HasKey("UserUID", "OtherUserUID")
                        .HasName("pk_client_pairs");

                    b.HasIndex("OtherUserUID")
                        .HasDatabaseName("ix_client_pairs_other_user_uid");

                    b.HasIndex("UserUID")
                        .HasDatabaseName("ix_client_pairs_user_uid");

                    b.ToTable("client_pairs", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.FileCache", b =>
                {
                    b.Property<string>("Hash")
                        .HasMaxLength(40)
                        .HasColumnType("character varying(40)")
                        .HasColumnName("hash");

                    b.Property<long>("RawSize")
                        .HasColumnType("bigint")
                        .HasColumnName("raw_size");

                    b.Property<long>("Size")
                        .HasColumnType("bigint")
                        .HasColumnName("size");

                    b.Property<byte[]>("Timestamp")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("timestamp");

                    b.Property<DateTime>("UploadDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("upload_date");

                    b.Property<bool>("Uploaded")
                        .HasColumnType("boolean")
                        .HasColumnName("uploaded");

                    b.Property<string>("UploaderUID")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("uploader_uid");

                    b.HasKey("Hash")
                        .HasName("pk_file_caches");

                    b.HasIndex("UploaderUID")
                        .HasDatabaseName("ix_file_caches_uploader_uid");

                    b.ToTable("file_caches", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.ForbiddenUploadEntry", b =>
                {
                    b.Property<string>("Hash")
                        .HasMaxLength(40)
                        .HasColumnType("character varying(40)")
                        .HasColumnName("hash");

                    b.Property<string>("ForbiddenBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("forbidden_by");

                    b.Property<byte[]>("Timestamp")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("timestamp");

                    b.HasKey("Hash")
                        .HasName("pk_forbidden_upload_entries");

                    b.ToTable("forbidden_upload_entries", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.Group", b =>
                {
                    b.Property<string>("GID")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("gid");

                    b.Property<string>("Alias")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("alias");

                    b.Property<string>("HashedPassword")
                        .HasColumnType("text")
                        .HasColumnName("hashed_password");

                    b.Property<bool>("InvitesEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("invites_enabled");

                    b.Property<string>("OwnerUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("owner_uid");

                    b.Property<bool>("PreferDisableAnimations")
                        .HasColumnType("boolean")
                        .HasColumnName("prefer_disable_animations");

                    b.Property<bool>("PreferDisableSounds")
                        .HasColumnType("boolean")
                        .HasColumnName("prefer_disable_sounds");

                    b.Property<bool>("PreferDisableVFX")
                        .HasColumnType("boolean")
                        .HasColumnName("prefer_disable_vfx");

                    b.HasKey("GID")
                        .HasName("pk_groups");

                    b.HasIndex("OwnerUID")
                        .HasDatabaseName("ix_groups_owner_uid");

                    b.ToTable("groups", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.GroupBan", b =>
                {
                    b.Property<string>("GroupGID")
                        .HasColumnType("character varying(20)")
                        .HasColumnName("group_gid");

                    b.Property<string>("BannedUserUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("banned_user_uid");

                    b.Property<string>("BannedByUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("banned_by_uid");

                    b.Property<DateTime>("BannedOn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("banned_on");

                    b.Property<string>("BannedReason")
                        .HasColumnType("text")
                        .HasColumnName("banned_reason");

                    b.HasKey("GroupGID", "BannedUserUID")
                        .HasName("pk_group_bans");

                    b.HasIndex("BannedByUID")
                        .HasDatabaseName("ix_group_bans_banned_by_uid");

                    b.HasIndex("BannedUserUID")
                        .HasDatabaseName("ix_group_bans_banned_user_uid");

                    b.HasIndex("GroupGID")
                        .HasDatabaseName("ix_group_bans_group_gid");

                    b.ToTable("group_bans", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.GroupPair", b =>
                {
                    b.Property<string>("GroupGID")
                        .HasColumnType("character varying(20)")
                        .HasColumnName("group_gid");

                    b.Property<string>("GroupUserUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("group_user_uid");

                    b.Property<bool>("IsModerator")
                        .HasColumnType("boolean")
                        .HasColumnName("is_moderator");

                    b.Property<bool>("IsPinned")
                        .HasColumnType("boolean")
                        .HasColumnName("is_pinned");

                    b.HasKey("GroupGID", "GroupUserUID")
                        .HasName("pk_group_pairs");

                    b.HasIndex("GroupGID")
                        .HasDatabaseName("ix_group_pairs_group_gid");

                    b.HasIndex("GroupUserUID")
                        .HasDatabaseName("ix_group_pairs_group_user_uid");

                    b.ToTable("group_pairs", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.GroupPairPreferredPermission", b =>
                {
                    b.Property<string>("UserUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("user_uid");

                    b.Property<string>("GroupGID")
                        .HasColumnType("character varying(20)")
                        .HasColumnName("group_gid");

                    b.Property<bool>("DisableAnimations")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_animations");

                    b.Property<bool>("DisableSounds")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_sounds");

                    b.Property<bool>("DisableVFX")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_vfx");

                    b.Property<bool>("IsPaused")
                        .HasColumnType("boolean")
                        .HasColumnName("is_paused");

                    b.HasKey("UserUID", "GroupGID")
                        .HasName("pk_group_pair_preferred_permissions");

                    b.HasIndex("GroupGID")
                        .HasDatabaseName("ix_group_pair_preferred_permissions_group_gid");

                    b.HasIndex("UserUID")
                        .HasDatabaseName("ix_group_pair_preferred_permissions_user_uid");

                    b.ToTable("group_pair_preferred_permissions", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.GroupTempInvite", b =>
                {
                    b.Property<string>("GroupGID")
                        .HasColumnType("character varying(20)")
                        .HasColumnName("group_gid");

                    b.Property<string>("Invite")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("invite");

                    b.Property<DateTime>("ExpirationDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expiration_date");

                    b.HasKey("GroupGID", "Invite")
                        .HasName("pk_group_temp_invites");

                    b.HasIndex("GroupGID")
                        .HasDatabaseName("ix_group_temp_invites_group_gid");

                    b.HasIndex("Invite")
                        .HasDatabaseName("ix_group_temp_invites_invite");

                    b.ToTable("group_temp_invites", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.LodeStoneAuth", b =>
                {
                    b.Property<decimal>("DiscordId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(20,0)")
                        .HasColumnName("discord_id");

                    b.Property<string>("HashedLodestoneId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("hashed_lodestone_id");

                    b.Property<string>("LodestoneAuthString")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("lodestone_auth_string");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("started_at");

                    b.Property<string>("UserUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("user_uid");

                    b.HasKey("DiscordId")
                        .HasName("pk_lodestone_auth");

                    b.HasIndex("UserUID")
                        .HasDatabaseName("ix_lodestone_auth_user_uid");

                    b.ToTable("lodestone_auth", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.User", b =>
                {
                    b.Property<string>("UID")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("uid");

                    b.Property<string>("Alias")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)")
                        .HasColumnName("alias");

                    b.Property<bool>("IsAdmin")
                        .HasColumnType("boolean")
                        .HasColumnName("is_admin");

                    b.Property<bool>("IsModerator")
                        .HasColumnType("boolean")
                        .HasColumnName("is_moderator");

                    b.Property<DateTime>("LastLoggedIn")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_logged_in");

                    b.Property<byte[]>("Timestamp")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea")
                        .HasColumnName("timestamp");

                    b.HasKey("UID")
                        .HasName("pk_users");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.UserDefaultPreferredPermission", b =>
                {
                    b.Property<string>("UserUID")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("user_uid");

                    b.Property<bool>("DisableGroupAnimations")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_group_animations");

                    b.Property<bool>("DisableGroupSounds")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_group_sounds");

                    b.Property<bool>("DisableGroupVFX")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_group_vfx");

                    b.Property<bool>("DisableIndividualAnimations")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_individual_animations");

                    b.Property<bool>("DisableIndividualSounds")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_individual_sounds");

                    b.Property<bool>("DisableIndividualVFX")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_individual_vfx");

                    b.Property<bool>("IndividualIsSticky")
                        .HasColumnType("boolean")
                        .HasColumnName("individual_is_sticky");

                    b.HasKey("UserUID")
                        .HasName("pk_user_default_preferred_permissions");

                    b.HasIndex("UserUID")
                        .HasDatabaseName("ix_user_default_preferred_permissions_user_uid");

                    b.ToTable("user_default_preferred_permissions", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.UserPermissionSet", b =>
                {
                    b.Property<string>("UserUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("user_uid");

                    b.Property<string>("OtherUserUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("other_user_uid");

                    b.Property<bool>("DisableAnimations")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_animations");

                    b.Property<bool>("DisableSounds")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_sounds");

                    b.Property<bool>("DisableVFX")
                        .HasColumnType("boolean")
                        .HasColumnName("disable_vfx");

                    b.Property<bool>("IsPaused")
                        .HasColumnType("boolean")
                        .HasColumnName("is_paused");

                    b.Property<bool>("Sticky")
                        .HasColumnType("boolean")
                        .HasColumnName("sticky");

                    b.HasKey("UserUID", "OtherUserUID")
                        .HasName("pk_user_permission_sets");

                    b.HasIndex("OtherUserUID")
                        .HasDatabaseName("ix_user_permission_sets_other_user_uid");

                    b.HasIndex("UserUID")
                        .HasDatabaseName("ix_user_permission_sets_user_uid");

                    b.HasIndex("UserUID", "OtherUserUID", "IsPaused")
                        .HasDatabaseName("ix_user_permission_sets_user_uid_other_user_uid_is_paused");

                    b.ToTable("user_permission_sets", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.UserProfileData", b =>
                {
                    b.Property<string>("UserUID")
                        .HasColumnType("character varying(10)")
                        .HasColumnName("user_uid");

                    b.Property<string>("Base64ProfileImage")
                        .HasColumnType("text")
                        .HasColumnName("base64profile_image");

                    b.Property<bool>("FlaggedForReport")
                        .HasColumnType("boolean")
                        .HasColumnName("flagged_for_report");

                    b.Property<bool>("IsNSFW")
                        .HasColumnType("boolean")
                        .HasColumnName("is_nsfw");

                    b.Property<bool>("ProfileDisabled")
                        .HasColumnType("boolean")
                        .HasColumnName("profile_disabled");

                    b.Property<string>("UserDescription")
                        .HasColumnType("text")
                        .HasColumnName("user_description");

                    b.HasKey("UserUID")
                        .HasName("pk_user_profile_data");

                    b.ToTable("user_profile_data", (string)null);
                });

            modelBuilder.Entity("MareSynchronosShared.Models.Auth", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.User", "PrimaryUser")
                        .WithMany()
                        .HasForeignKey("PrimaryUserUID")
                        .HasConstraintName("fk_auth_users_primary_user_uid");

                    b.HasOne("MareSynchronosShared.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserUID")
                        .HasConstraintName("fk_auth_users_user_uid");

                    b.Navigation("PrimaryUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaData", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.User", "Uploader")
                        .WithMany()
                        .HasForeignKey("UploaderUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_chara_data_users_uploader_uid");

                    b.Navigation("Uploader");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaDataAllowance", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.User", "AllowedUser")
                        .WithMany()
                        .HasForeignKey("AllowedUserUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_chara_data_allowance_users_allowed_user_uid");

                    b.HasOne("MareSynchronosShared.Models.CharaData", "Parent")
                        .WithMany("AllowedIndividiuals")
                        .HasForeignKey("ParentId", "ParentUploaderUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_chara_data_allowance_chara_data_parent_id_parent_uploader_u");

                    b.Navigation("AllowedUser");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaDataFile", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.FileCache", "FileCache")
                        .WithMany()
                        .HasForeignKey("FileCacheHash")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_chara_data_files_files_file_cache_hash");

                    b.HasOne("MareSynchronosShared.Models.CharaData", "Parent")
                        .WithMany("Files")
                        .HasForeignKey("ParentId", "ParentUploaderUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_chara_data_files_chara_data_parent_id_parent_uploader_uid");

                    b.Navigation("FileCache");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaDataFileSwap", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.CharaData", "Parent")
                        .WithMany("FileSwaps")
                        .HasForeignKey("ParentId", "ParentUploaderUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_chara_data_file_swaps_chara_data_parent_id_parent_uploader_");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaDataOriginalFile", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.CharaData", "Parent")
                        .WithMany("OriginalFiles")
                        .HasForeignKey("ParentId", "ParentUploaderUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_chara_data_orig_files_chara_data_parent_id_parent_uploader_");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaDataPose", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.CharaData", "Parent")
                        .WithMany("Poses")
                        .HasForeignKey("ParentId", "ParentUploaderUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_chara_data_poses_chara_data_parent_id_parent_uploader_uid");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.ClientPair", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.User", "OtherUser")
                        .WithMany()
                        .HasForeignKey("OtherUserUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_client_pairs_users_other_user_uid");

                    b.HasOne("MareSynchronosShared.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_client_pairs_users_user_uid");

                    b.Navigation("OtherUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.FileCache", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.User", "Uploader")
                        .WithMany()
                        .HasForeignKey("UploaderUID")
                        .HasConstraintName("fk_file_caches_users_uploader_uid");

                    b.Navigation("Uploader");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.Group", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.User", "Owner")
                        .WithMany()
                        .HasForeignKey("OwnerUID")
                        .HasConstraintName("fk_groups_users_owner_uid");

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.GroupBan", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.User", "BannedBy")
                        .WithMany()
                        .HasForeignKey("BannedByUID")
                        .HasConstraintName("fk_group_bans_users_banned_by_uid");

                    b.HasOne("MareSynchronosShared.Models.User", "BannedUser")
                        .WithMany()
                        .HasForeignKey("BannedUserUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_group_bans_users_banned_user_uid");

                    b.HasOne("MareSynchronosShared.Models.Group", "Group")
                        .WithMany()
                        .HasForeignKey("GroupGID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_group_bans_groups_group_gid");

                    b.Navigation("BannedBy");

                    b.Navigation("BannedUser");

                    b.Navigation("Group");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.GroupPair", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.Group", "Group")
                        .WithMany()
                        .HasForeignKey("GroupGID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_group_pairs_groups_group_gid");

                    b.HasOne("MareSynchronosShared.Models.User", "GroupUser")
                        .WithMany()
                        .HasForeignKey("GroupUserUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_group_pairs_users_group_user_uid");

                    b.Navigation("Group");

                    b.Navigation("GroupUser");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.GroupPairPreferredPermission", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.Group", "Group")
                        .WithMany()
                        .HasForeignKey("GroupGID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_group_pair_preferred_permissions_groups_group_gid");

                    b.HasOne("MareSynchronosShared.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_group_pair_preferred_permissions_users_user_uid");

                    b.Navigation("Group");

                    b.Navigation("User");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.GroupTempInvite", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.Group", "Group")
                        .WithMany()
                        .HasForeignKey("GroupGID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_group_temp_invites_groups_group_gid");

                    b.Navigation("Group");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.LodeStoneAuth", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserUID")
                        .HasConstraintName("fk_lodestone_auth_users_user_uid");

                    b.Navigation("User");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.UserDefaultPreferredPermission", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_default_preferred_permissions_users_user_uid");

                    b.Navigation("User");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.UserPermissionSet", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.User", "OtherUser")
                        .WithMany()
                        .HasForeignKey("OtherUserUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_permission_sets_users_other_user_uid");

                    b.HasOne("MareSynchronosShared.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_permission_sets_users_user_uid");

                    b.Navigation("OtherUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.UserProfileData", b =>
                {
                    b.HasOne("MareSynchronosShared.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserUID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_profile_data_users_user_uid");

                    b.Navigation("User");
                });

            modelBuilder.Entity("MareSynchronosShared.Models.CharaData", b =>
                {
                    b.Navigation("AllowedIndividiuals");

                    b.Navigation("FileSwaps");

                    b.Navigation("Files");

                    b.Navigation("OriginalFiles");

                    b.Navigation("Poses");
                });
#pragma warning restore 612, 618
        }
    }
}
